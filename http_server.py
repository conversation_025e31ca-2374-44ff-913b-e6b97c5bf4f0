#!/usr/bin/env python3
"""
MCP Streamable HTTP 服务器实现
符合 MCP 协议规范的 HTTP 传输层
"""

import asyncio
import json
import argparse
import uuid
from datetime import datetime
from typing import Any, Dict, Optional
from fastapi import FastAPI, HTTPException, Request, Response, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.lowlevel import NotificationOptions
from mcp.types import Tool, TextContent
import mcp.types as types
from knowledge import Knowledge
from logger import Logger

# 设置日志
logger = Logger(log_file="logs/mcp_http_server.log", name="MCP_HTTP_Server")

# 初始化知识实例
knowledge_instance = Knowledge()

# 创建 MCP 服务器实例
mcp_server = Server("knowledge-server")

# 会话管理
sessions: Dict[str, Dict[str, Any]] = {}

# 创建 FastAPI 应用
app = FastAPI(
    title="MCP Knowledge Server",
    description="MCP 协议兼容的知识库服务器",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# MCP 工具定义
@mcp_server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """List available tools."""
    logger.info("客户端请求工具列表")
    return [
        Tool(
            name="get_knowledge_data",
            description="获取知识数据，基于查询文本搜索相关文档并返回格式化的知识内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "查询文本，用于搜索相关文档"
                    }
                },
                "required": ["query"]
            }
        )
    ]

@mcp_server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any]) -> list[types.TextContent]:
    """Handle tool calls."""
    logger.info(f"收到工具调用请求: {name}, 参数: {arguments}")

    if name == "get_knowledge_data":
        try:
            query = arguments.get("query")

            if not query:
                logger.warning("查询文本为空")
                return [types.TextContent(
                    type="text",
                    text="错误：查询文本不能为空"
                )]

            logger.info(f"开始处理知识查询: {query}")
            # Call the knowledge service
            result = knowledge_instance.get_knowledge_data(query, 0)
            logger.info(f"知识查询完成，返回结果长度: {len(result) if result else 0}")

            return [types.TextContent(
                type="text",
                text=result
            )]

        except Exception as e:
            logger.error(f"调用知识服务时发生错误: {str(e)}", exc_info=True)
            return [types.TextContent(
                type="text",
                text=f"调用知识服务时发生错误: {str(e)}"
            )]
    else:
        logger.error(f"未知工具: {name}")
        raise ValueError(f"Unknown tool: {name}")

def create_session_id() -> str:
    """创建新的会话ID"""
    return str(uuid.uuid4())

def validate_protocol_version(version: Optional[str]) -> bool:
    """验证 MCP 协议版本"""
    supported_versions = ["2024-11-05", "2025-03-26", "2025-06-18"]
    if not version:
        return True  # 允许空版本，默认使用 2024-11-05
    return version in supported_versions

async def process_jsonrpc_message(message: dict, session_id: Optional[str] = None) -> dict:
    """处理 JSON-RPC 消息"""
    try:
        method = message.get("method")
        params = message.get("params", {})
        msg_id = message.get("id")

        logger.info(f"处理 JSON-RPC 消息: {method}")

        if method == "initialize":
            # 获取客户端请求的协议版本，默认使用 2024-11-05
            client_version = params.get("protocolVersion", "2024-11-05")
            # 选择支持的版本，优先使用客户端版本
            if client_version in ["2024-11-05", "2025-03-26", "2025-06-18"]:
                protocol_version = client_version
            else:
                protocol_version = "2024-11-05"  # 默认使用最兼容的版本

            # 初始化请求 - 手动构建 capabilities 以避免序列化问题
            result = {
                "protocolVersion": protocol_version,
                "capabilities": {
                    "tools": {
                        "listChanged": False
                    },
                    "resources": {},
                    "prompts": {},
                    "logging": {},
                    "experimental": {}
                },
                "serverInfo": {
                    "name": "knowledge-server",
                    "version": "1.0.0"
                }
            }
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "result": result
            }

        elif method == "tools/list":
            # 列出工具
            tools = await handle_list_tools()
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "result": {
                    "tools": [tool.model_dump() for tool in tools]
                }
            }

        elif method == "tools/call":
            # 调用工具
            tool_name = params.get("name")
            arguments = params.get("arguments", {})

            result = await handle_call_tool(tool_name, arguments)
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "result": {
                    "content": [content.model_dump() for content in result]
                }
            }

        else:
            # 未知方法
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }

    except Exception as e:
        logger.error(f"处理 JSON-RPC 消息时发生错误: {str(e)}", exc_info=True)
        return {
            "jsonrpc": "2.0",
            "id": message.get("id"),
            "error": {
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        }

# MCP 协议端点
@app.post("/mcp")
async def mcp_endpoint(
    request: Request,
    mcp_protocol_version: Optional[str] = Header(None, alias="MCP-Protocol-Version"),
    mcp_session_id: Optional[str] = Header(None, alias="Mcp-Session-Id"),
    accept: str = Header(...)
):
    """MCP 协议主端点 - 处理 JSON-RPC 消息"""

    # 验证协议版本
    if not validate_protocol_version(mcp_protocol_version):
        raise HTTPException(status_code=400, detail=f"Unsupported protocol version: {mcp_protocol_version}")

    # 验证 Origin 头（安全要求）
    origin = request.headers.get("origin")
    if origin and not origin.startswith("http://127.0.0.1") and not origin.startswith("http://localhost"):
        logger.warning(f"可疑的 Origin 头: {origin}")

    try:
        # 解析 JSON-RPC 消息
        body = await request.body()
        message = json.loads(body.decode('utf-8'))

        logger.info(f"收到 MCP 消息: {message.get('method', 'unknown')}")

        # 处理初始化消息的会话管理
        if message.get("method") == "initialize" and not mcp_session_id:
            session_id = create_session_id()
            sessions[session_id] = {
                "created_at": datetime.now(),
                "protocol_version": mcp_protocol_version or "2024-11-05"
            }
            logger.info(f"创建新会话: {session_id}")
        else:
            session_id = mcp_session_id

        # 验证会话
        if session_id and session_id not in sessions:
            raise HTTPException(status_code=404, detail="Session not found")

        # 处理消息
        response_data = await process_jsonrpc_message(message, session_id)

        # 如果是初始化响应，添加会话ID头
        if message.get("method") == "initialize" and session_id:
            response = Response(
                content=json.dumps(response_data),
                media_type="application/json",
                headers={"Mcp-Session-Id": session_id}
            )
            return response

        return response_data

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON")
    except Exception as e:
        logger.error(f"处理 MCP 请求时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mcp")
async def mcp_sse_endpoint(
    mcp_protocol_version: Optional[str] = Header(None, alias="MCP-Protocol-Version"),
    mcp_session_id: Optional[str] = Header(None, alias="Mcp-Session-Id"),
    accept: str = Header(...)
):
    """MCP SSE 端点 - 用于服务器到客户端的消息流"""

    if "text/event-stream" not in accept:
        raise HTTPException(status_code=405, detail="Method Not Allowed")

    # 验证会话
    if mcp_session_id and mcp_session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    async def event_stream():
        """SSE 事件流生成器"""
        try:
            # 发送连接确认
            yield f"data: {json.dumps({'type': 'connection', 'status': 'connected'})}\n\n"

            # 保持连接活跃
            while True:
                await asyncio.sleep(30)  # 每30秒发送心跳
                yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': datetime.now().isoformat()})}\n\n"

        except Exception as e:
            logger.error(f"SSE 流错误: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

@app.delete("/mcp")
async def terminate_session(
    mcp_session_id: Optional[str] = Header(None, alias="Mcp-Session-Id")
):
    """终止 MCP 会话"""
    if mcp_session_id and mcp_session_id in sessions:
        del sessions[mcp_session_id]
        logger.info(f"会话已终止: {mcp_session_id}")
        return {"status": "session terminated"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")

# 健康检查端点（非 MCP 协议）
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "protocol": "MCP",
        "version": "1.0.0",
        "active_sessions": len(sessions),
        "timestamp": datetime.now().isoformat()
    }

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="MCP HTTP Knowledge Server")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址 (默认: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="日志级别 (默认: INFO)")
    parser.add_argument("--reload", action="store_true", help="启用自动重载 (开发模式)")
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()

    logger.info("=" * 60)
    logger.info("MCP HTTP Knowledge Server 启动中...")
    logger.info(f"协议: Model Context Protocol (MCP)")
    logger.info(f"传输方式: Streamable HTTP")
    logger.info(f"主机地址: {args.host}")
    logger.info(f"端口: {args.port}")
    logger.info(f"日志级别: {args.log_level}")
    logger.info(f"自动重载: {'启用' if args.reload else '禁用'}")
    logger.info(f"MCP 端点: http://{args.host}:{args.port}/mcp")
    logger.info(f"健康检查: http://{args.host}:{args.port}/health")
    logger.info("=" * 60)
    logger.info("支持的 MCP 协议版本: 2024-11-05 (默认), 2025-03-26, 2025-06-18")
    logger.info("支持的功能: 工具调用, 会话管理, SSE 流")
    logger.info("=" * 60)

    try:
        uvicorn.run(
            "http_server:app",
            host=args.host,
            port=args.port,
            log_level=args.log_level.lower(),
            reload=args.reload
        )
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}", exc_info=True)
        raise
    finally:
        logger.info("MCP HTTP Knowledge Server 已关闭")

if __name__ == "__main__":
    main()
