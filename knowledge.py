from milvus_manger import VectorStoreManager

class Knowledge:
    def __init__(self):
        self.vector_store = VectorStoreManager()

    def process_docs(self, docs, select_num, search_docs, push_date_dict):
        """
        处理文档并进行时间加权排序
        """
        temp_df = self.vector_store.parent_child_docs_time_weight(docs)
        selected = 0
        
        for _, temp_row in temp_df.iterrows():
            if selected >= select_num:
                break
            doc_text = temp_row['parent_document']
            if '【长韭杯】' in doc_text:
                continue
            
            if doc_text not in search_docs:
                push_date_dict[doc_text] = temp_row
                search_docs.append(doc_text)
                selected += 1
                    
        return search_docs, push_date_dict

    def get_knowledge_data(self, query: str, min_score: float = 0):
        """
        获取知识数据
        """
        search_docs = []
        push_date_dict = {}

        # 顺序执行搜索任务
        jygs_docs = self.vector_store.search_articles(query, semantic_weight=0.7, k=20, min_score=min_score)
        ind_docs = self.vector_store.search_industry(query, semantic_weight=0.7, k=3, min_score=min_score)
        wx_docs = self.vector_store.search_wx_articles(query, semantic_weight=0.7, k=5, min_score=min_score)

        full_docs = jygs_docs + ind_docs + wx_docs

        # 时间加权排序
        search_docs, push_date_dict = self.process_docs(full_docs, 10, search_docs, push_date_dict)
        
        # rerank重排序
        rerank_docs = self.vector_store.rerank(query, search_docs)
        
        # 输入控制在10000字以内
        input_knowledge = ""
        knowledge_i = 1
        
        for temp_doc in rerank_docs:
            document = temp_doc['document']
            push_dt = self.vector_store.timestamp_to_date(push_date_dict[document]['push_datetime'])
            fix_doc_text = f"发布时间：{push_dt}\n\n{document}"
            input_knowledge += f"【参考资料{knowledge_i}】\n```markdown\n{fix_doc_text}\n```\n\n"
            if len(input_knowledge) > 10000:
                break
            knowledge_i += 1
            
        return input_knowledge
