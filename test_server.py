#!/usr/bin/env python3
"""
服务器测试脚本
用于测试 HTTP 服务器的功能
"""

import requests
import json
import argparse
import time
from logger import Logger

# 设置日志
logger = Logger(log_file="logs/test_server.log", name="ServerTester")

def test_health_check(base_url):
    """测试健康检查接口"""
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✓ 健康检查通过")
            return True
        else:
            logger.error(f"✗ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"✗ 健康检查异常: {str(e)}")
        return False

def test_root_endpoint(base_url):
    """测试根路径接口"""
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info("✓ 根路径访问成功")
            logger.info(f"  服务版本: {data.get('version', 'unknown')}")
            return True
        else:
            logger.error(f"✗ 根路径访问失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"✗ 根路径访问异常: {str(e)}")
        return False

def test_tools_endpoint(base_url):
    """测试工具列表接口"""
    try:
        response = requests.get(f"{base_url}/tools", timeout=5)
        if response.status_code == 200:
            data = response.json()
            tools = data.get('tools', [])
            logger.info("✓ 工具列表获取成功")
            logger.info(f"  可用工具数量: {len(tools)}")
            for tool in tools:
                logger.info(f"  - {tool.get('name', 'unknown')}: {tool.get('description', 'no description')}")
            return True
        else:
            logger.error(f"✗ 工具列表获取失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"✗ 工具列表获取异常: {str(e)}")
        return False

def test_query_endpoint(base_url, query_text):
    """测试查询接口"""
    try:
        payload = {"query": query_text}
        response = requests.post(f"{base_url}/query", json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                result_data = data.get('data', '')
                logger.info("✓ 知识查询成功")
                logger.info(f"  查询文本: {query_text}")
                logger.info(f"  返回数据长度: {len(result_data) if result_data else 0}")
                if result_data:
                    # 显示前200个字符
                    preview = result_data[:200] + "..." if len(result_data) > 200 else result_data
                    logger.info(f"  结果预览: {preview}")
                return True
            else:
                error_msg = data.get('error', 'unknown error')
                logger.error(f"✗ 知识查询失败: {error_msg}")
                return False
        else:
            logger.error(f"✗ 知识查询请求失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"✗ 知识查询异常: {str(e)}")
        return False

def run_comprehensive_test(base_url, query_text):
    """运行综合测试"""
    logger.info("=" * 60)
    logger.info("开始服务器功能测试")
    logger.info(f"目标服务器: {base_url}")
    logger.info("=" * 60)
    
    tests = [
        ("健康检查", lambda: test_health_check(base_url)),
        ("根路径访问", lambda: test_root_endpoint(base_url)),
        ("工具列表", lambda: test_tools_endpoint(base_url)),
        ("知识查询", lambda: test_query_endpoint(base_url, query_text)),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n正在测试: {test_name}")
        if test_func():
            passed += 1
        time.sleep(1)  # 间隔1秒
    
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {passed}/{total} 项测试通过")
    if passed == total:
        logger.info("✓ 所有测试通过，服务器运行正常")
    else:
        logger.warning(f"✗ {total - passed} 项测试失败，请检查服务器状态")
    logger.info("=" * 60)
    
    return passed == total

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Knowledge Server 测试工具")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址 (默认: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--query", default="人工智能", help="测试查询文本 (默认: 人工智能)")
    parser.add_argument("--quick", action="store_true", help="快速测试 (仅健康检查)")
    
    args = parser.parse_args()
    
    base_url = f"http://{args.host}:{args.port}"
    
    if args.quick:
        logger.info("执行快速健康检查...")
        if test_health_check(base_url):
            logger.info("✓ 服务器运行正常")
        else:
            logger.error("✗ 服务器无响应")
    else:
        run_comprehensive_test(base_url, args.query)

if __name__ == "__main__":
    main()
