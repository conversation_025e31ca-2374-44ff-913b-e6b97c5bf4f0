#!/usr/bin/env python3
"""
服务器启动脚本
支持启动 MCP 服务器或 HTTP 服务器
"""

import argparse
import subprocess
import sys
import os
from logger import Logger

# 设置日志
logger = Logger(log_file="logs/start_server.log", name="ServerStarter")

def run_mcp_server(args):
    """运行 MCP 服务器"""
    cmd = [sys.executable, "mcp_server.py", "--stdio"]
    
    if args.log_level:
        cmd.extend(["--log-level", args.log_level])
    
    logger.info(f"启动 MCP 服务器: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        logger.info("MCP 服务器被用户中断")
    except subprocess.CalledProcessError as e:
        logger.error(f"MCP 服务器启动失败: {e}")
        sys.exit(1)

def run_http_server(args):
    """运行 HTTP 服务器"""
    cmd = [sys.executable, "http_server.py"]
    
    if args.host:
        cmd.extend(["--host", args.host])
    if args.port:
        cmd.extend(["--port", str(args.port)])
    if args.log_level:
        cmd.extend(["--log-level", args.log_level])
    if args.reload:
        cmd.append("--reload")
    
    logger.info(f"启动 HTTP 服务器: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        logger.info("HTTP 服务器被用户中断")
    except subprocess.CalledProcessError as e:
        logger.error(f"HTTP 服务器启动失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Knowledge Server 启动器")
    
    # 服务器类型选择
    parser.add_argument("mode", choices=["mcp", "http"], 
                       help="服务器模式: mcp (MCP协议) 或 http (HTTP API)")
    
    # 通用参数
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="日志级别 (默认: INFO)")
    
    # HTTP 服务器专用参数
    parser.add_argument("--host", default="127.0.0.1", 
                       help="HTTP 服务器主机地址 (默认: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, 
                       help="HTTP 服务器端口 (默认: 8000)")
    parser.add_argument("--reload", action="store_true", 
                       help="HTTP 服务器自动重载 (开发模式)")
    
    args = parser.parse_args()
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    logger.info("=" * 60)
    logger.info("Knowledge Server 启动器")
    logger.info(f"模式: {args.mode.upper()}")
    logger.info("=" * 60)
    
    if args.mode == "mcp":
        logger.info("启动 MCP 服务器模式")
        logger.info("用于与 MCP 客户端通信")
        run_mcp_server(args)
    elif args.mode == "http":
        logger.info("启动 HTTP 服务器模式")
        logger.info(f"HTTP API 地址: http://{args.host}:{args.port}")
        logger.info(f"API 文档地址: http://{args.host}:{args.port}/docs")
        run_http_server(args)

if __name__ == "__main__":
    main()
