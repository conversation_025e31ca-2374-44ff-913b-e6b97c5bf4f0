from milvus_lite import VectorStore
import json
import threading
from logger import Logger
import time
import backoff
import os
from queue import Queue
from pymilvus import connections
from dotenv import load_dotenv

load_dotenv()

class ConnectionPool:
    def __init__(self, max_connections=10, **kwargs):
        self.max_connections = max_connections
        self.connection_kwargs = kwargs
        self.pool = Queue(maxsize=max_connections)
        self.lock = threading.Lock()
        self._fill_pool()
    
    def _fill_pool(self):
        """初始化连接池"""
        for _ in range(self.max_connections):
            connection = self._create_connection()
            self.pool.put(connection)
    
    def _create_connection(self):
        """创建新的数据库连接"""
        try:
            # 解析URI
            uri = str(self.connection_kwargs.get('uri', ''))
            if not uri:
                raise ValueError("未提供host参数")
            
            # 移除协议前缀
            if uri.startswith('http://'):
                uri = uri[7:]
            elif uri.startswith('https://'):
                uri = uri[8:]
            
            # 分离主机名和端口
            if ':' in uri:
                host, port = uri.split(':')
                port = int(port)
            else:
                host = uri
                port = 19530  # Milvus默认端口
                
            user = str(self.connection_kwargs.get('username', ''))
            password = str(self.connection_kwargs.get('password', ''))
            
            connection = connections.connect(
                alias=f"conn_{threading.get_ident()}",
                host=host,
                port=port,
                user=user,
                password=password
            )
            return connection
        except Exception as e:
            raise Exception(f"创建数据库连接失败: {str(e)}")
    
    def get_connection(self):
        """从连接池获取连接"""
        try:
            connection = self.pool.get(timeout=5)
            return connection
        except Exception:
            with self.lock:
                if self.pool.qsize() < self.max_connections:
                    connection = self._create_connection()
                    return connection
                raise Exception("连接池已满，无法创建新连接")
    
    def release_connection(self, connection):
        """释放连接回连接池"""
        try:
            self.pool.put(connection, timeout=5)
        except Exception:
            connection.close()

class VectorStoreManager(VectorStore):
    """向量库连接管理器，支持自动重连"""
    _instance = None
    _lock = threading.Lock()
    _pool = None
    
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super(VectorStoreManager, cls).__new__(cls)
                    # 从kwargs中获取参数，如果没有则从配置文件中读取
                    if not kwargs:
                        try:
                            with open("config.json", "r", encoding="utf-8") as f:
                                config = json.load(f)
                                milvus_config = config['milvus']
                                kwargs = {
                                    'milvus_url': milvus_config['url'],
                                    'milvus_user': milvus_config['user'],
                                    'milvus_password': milvus_config['password']
                                }
                        except Exception as e:
                            raise Exception(f"无法读取配置文件: {str(e)}")
                    
                    cls._pool = ConnectionPool(
                        max_connections=10,
                        uri=str(kwargs.get('milvus_url', '')),
                        username=str(kwargs.get('milvus_user', '')),
                        password=str(kwargs.get('milvus_password', ''))
                    )
        return cls._instance

    def __init__(self, 
                 default_collection: str="rag_jygs_articles", 
                 config_path: str="",
                 max_retries: int = 3, 
                 retry_delay: float = 1.0
                 ):
        """
        向量库连接管理器，支持自动重连

        Params:
            default_collection: 默认的集合名称
            config_path: 配置文件路径
            max_retries: 最大重试次数
            retry_delay: 重试间隔时间(秒)
        """
        if not hasattr(self, 'initialized'):
            if config_path:
                self.config = json.load(open(config_path, "r", encoding="utf-8"))
            else:
                # 使用当前python文件的目录
                self.config = json.load(open("config.json", "r", encoding="utf-8"))
            self.milvus_config = self.config['milvus']
            self.openai_config = self.config['openai']
            self.milvus_url = self.milvus_config['url']
            self.milvus_user = self.milvus_config['user']
            self.milvus_password = self.milvus_config['password']
            self.openai_url = os.getenv('OPENAI_API_URL') or self.openai_config['api_url']
            self.openai_api_key = os.getenv('OPENAI_API_KEY') or self.openai_config['api_key']
            self.openai_rerank_url = os.getenv('OPENAI_API_RERANK_URL') or self.openai_config['api_rerank_url']
            self.data_path = self.config['data_path']
            if self.data_path.endswith('/'):
                self.data_path = self.data_path[:-1]
            self.max_retries = max_retries
            self.retry_delay = retry_delay
            self._local = threading.local()
            self.db_articles_summary = None
            self.db_industry = None
            self.db_articles = None
            self.db_wx_articles_summary = None
            self._last_connection_check = 0
            self._check_interval = 60  # 连接状态检查间隔（秒）
            self.ind_data = {}

            default_chunk_strategy = self.milvus_config['collections'][default_collection]['chunk_strategy']

            self.initialized = True

            super().__init__(
                milvus_url=self.milvus_url,
                milvus_user=self.milvus_user,
                milvus_password=self.milvus_password,
                collection_name=default_collection,
                chunk_strategy=default_chunk_strategy,
                openai_url=self.openai_url,
                openai_api_key=self.openai_api_key
            )

            self.logger = Logger(log_file=f"logs/VectorStoreManager.log", name="VectorStoreManager")
            
            # 初始化连接
            self.init_connections()

            # 加载题材库数据
            self.load_ind_data()

            self._text_hash_map = {}

    def load_ind_data(self):
        """加载题材库数据"""
        for file in os.listdir(self.data_path + '/jygs/jygs_ind'):
            with open(self.data_path + '/jygs/jygs_ind/' + file, 'r', encoding='utf-8') as f:
                self.ind_data[file] = f.read()

    def read_ind_data(self, file_name: str):
        """读取题材库数据"""
        if file_name.startswith('/'):
            file_name = file_name[1:]
        if file_name not in self.ind_data:
            if os.path.exists(self.data_path + '/jygs/jygs_ind/' + file_name):
                with open(self.data_path + '/jygs/jygs_ind/' + file_name, 'r', encoding='utf-8') as f:
                    self.ind_data[file_name] = f.read()
        return self.ind_data[file_name]
        
    def init_connections(self):
        """初始化向量库连接"""
        with self._lock:
            try:
                self.db_articles_summary = VectorStore(milvus_url=self.milvus_url,
                                        milvus_user=self.milvus_user,
                                        milvus_password=self.milvus_password,
                                        collection_name='rag_jygs_articles_summary',
                                        chunk_strategy="parent_child_markdown",
                                        rerank_url=self.openai_rerank_url,
                                        openai_url=self.openai_url,
                                        openai_api_key=self.openai_api_key
                                    )
                self.db_industry = VectorStore(milvus_url=self.milvus_url,
                                        milvus_user=self.milvus_user,
                                        milvus_password=self.milvus_password,
                                        collection_name='rag_jygs_industry',
                                        chunk_strategy="parent_child",
                                        rerank_url=self.openai_rerank_url,
                                        openai_url=self.openai_url,
                                        openai_api_key=self.openai_api_key
                                    )
                self.db_articles = VectorStore(milvus_url=self.milvus_url,
                                        milvus_user=self.milvus_user,
                                        milvus_password=self.milvus_password,
                                        collection_name='rag_jygs_articles',
                                        chunk_strategy= 'parent_child',
                                        rerank_url=self.openai_rerank_url,
                                        openai_url=self.openai_url,
                                        openai_api_key=self.openai_api_key
                                    )
                self.db_wx_articles_summary = VectorStore(milvus_url=self.milvus_url,
                                        milvus_user=self.milvus_user,
                                        milvus_password=self.milvus_password,
                                        collection_name='rag_wx_articles_summary',
                                        chunk_strategy="parent_child_markdown",
                                        rerank_url=self.openai_rerank_url,
                                        openai_url=self.openai_url,
                                        openai_api_key=self.openai_api_key
                                    )
                self._last_connection_check = time.time()
            except Exception as e:
                self.logger.error(f"向量库初始化连接失败: {str(e)}", exc_info=True)
                raise
    
    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=3,
        max_time=30
    )
    def check_and_reconnect(self):
        """检查连接状态并在需要时重连"""
        current_time = time.time()
        if current_time - self._last_connection_check < self._check_interval:
            return
            
        with self._lock:
            try:
                # 尝试执行一个简单的操作来检查连接
                self.db_articles.collection.num_entities
                self.db_industry.collection.num_entities
                self.db_articles_summary.collection.num_entities
                self.db_wx_articles_summary.collection.num_entities
                self._last_connection_check = current_time
            except Exception as e:
                self.logger.error(f"向量库连接检查失败，尝试重连: {str(e)}", exc_info=True)
                self.init_connections()
    
    def search_articles(self, query: str, **kwargs):
        """搜索文章库"""
        self.check_and_reconnect()
        try:
            return self.db_articles_summary.search_hybrid_parent_child(query, **kwargs)
        except Exception as e:
            self.logger.error(f"文章库搜索失败: {str(e)}", exc_info=True)
            # 尝试重连并重试
            self.init_connections()
            return self.db_articles_summary.search_hybrid_parent_child(query, **kwargs)
    
    def search_industry(self, query: str, **kwargs):
        """搜索题材库"""
        self.check_and_reconnect()
        try:
            ind_docs = self.db_industry.search_hybrid_parent_child(query, **kwargs)
            # 修正题材库父文档
            for item in ind_docs:
                fn = item['parent_metadata']['file_path']
                item['parent_document'] = self.read_ind_data(fn)
            return ind_docs
        except Exception as e:
            self.logger.error(f"题材库搜索失败: {str(e)}", exc_info=True)
            # 尝试重连并重试
            self.init_connections()
            return self.db_industry.search_hybrid_parent_child(query, **kwargs)
        
    def search_wx_articles(self, query: str, **kwargs):
        """搜索微信文章库"""
        self.check_and_reconnect()
        try:
            return self.db_wx_articles_summary.search_hybrid_parent_child(query, **kwargs)
        except Exception as e:
            self.logger.error(f"微信文章库搜索失败: {str(e)}", exc_info=True)
    
    def rerank(self, query: str, documents: list, **kwargs):
        """重排序文档"""
        self.check_and_reconnect()
        try:
            return self.db_articles.rerank(query=query, documents=documents, **kwargs)
        except Exception as e:
            self.logger.error(f"重排序失败: {str(e)}", exc_info=True)
            # 尝试重连并重试
            self.init_connections()
            return self.db_articles.rerank(query=query, documents=documents, **kwargs)
