import asyncio
import json
import logging
import argparse
import sys
from datetime import datetime
from typing import Any, Sequence
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.server.lowlevel import NotificationOptions
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
import mcp.types as types
from knowledge import Knowledge
from logger import Logger

# 设置日志
logger = Logger(log_file="logs/mcp_server.log", name="MCP_Server")

# Initialize the knowledge instance
knowledge_instance = Knowledge()

# Create MCP server
server = Server("knowledge-server")

@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """List available tools."""
    logger.info("客户端请求工具列表")
    return [
        Tool(
            name="get_knowledge_data",
            description="获取知识数据，基于查询文本搜索相关文档并返回格式化的知识内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "查询文本，用于搜索相关文档"
                    }
                },
                "required": ["query"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any]) -> list[types.TextContent]:
    """Handle tool calls."""
    logger.info(f"收到工具调用请求: {name}, 参数: {arguments}")

    if name == "get_knowledge_data":
        try:
            query = arguments.get("query")

            if not query:
                logger.warning("查询文本为空")
                return [types.TextContent(
                    type="text",
                    text="错误：查询文本不能为空"
                )]

            logger.info(f"开始处理知识查询: {query}")
            # Call the knowledge service
            result = knowledge_instance.get_knowledge_data(query, 0)
            logger.info(f"知识查询完成，返回结果长度: {len(result) if result else 0}")

            return [types.TextContent(
                type="text",
                text=result
            )]

        except Exception as e:
            logger.error(f"调用知识服务时发生错误: {str(e)}", exc_info=True)
            return [types.TextContent(
                type="text",
                text=f"调用知识服务时发生错误: {str(e)}"
            )]
    else:
        logger.error(f"未知工具: {name}")
        raise ValueError(f"Unknown tool: {name}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="MCP Knowledge Server")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址 (默认: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="日志级别 (默认: INFO)")
    parser.add_argument("--stdio", action="store_true",
                       help="使用标准输入输出模式 (MCP 客户端通信)")
    return parser.parse_args()

async def main():
    """Main entry point for the MCP server."""
    args = parse_arguments()

    # 设置日志级别
    if args.log_level:
        logger.logger.setLevel(getattr(logging, args.log_level))

    logger.info("=" * 60)
    logger.info("MCP Knowledge Server 启动中...")
    logger.info(f"服务器名称: knowledge-server")
    logger.info(f"服务器版本: 1.0.0")
    logger.info(f"日志级别: {args.log_level}")

    if args.stdio:
        logger.info("运行模式: 标准输入输出 (MCP 客户端通信)")
        logger.info("等待 MCP 客户端连接...")
    else:
        logger.info(f"运行模式: HTTP 服务器")
        logger.info(f"主机地址: {args.host}")
        logger.info(f"端口: {args.port}")

    logger.info("=" * 60)

    try:
        if args.stdio:
            # 标准输入输出模式 (用于 MCP 客户端)
            async with stdio_server() as (read_stream, write_stream):
                logger.info("MCP 服务器已启动，等待客户端连接...")
                await server.run(
                    read_stream,
                    write_stream,
                    InitializationOptions(
                        server_name="knowledge-server",
                        server_version="1.0.0",
                        capabilities=server.get_capabilities(
                            notification_options=NotificationOptions(),
                            experimental_capabilities={},
                        )
                    )
                )
        else:
            # HTTP 服务器模式 (需要额外的 HTTP 服务器实现)
            logger.warning("HTTP 服务器模式尚未实现，请使用 --stdio 参数")
            logger.info("使用方法: python mcp_server.py --stdio")
            return

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器运行时发生错误: {str(e)}", exc_info=True)
        raise
    finally:
        logger.info("MCP Knowledge Server 已关闭")

if __name__ == "__main__":
    asyncio.run(main())