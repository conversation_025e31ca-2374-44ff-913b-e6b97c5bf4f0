# Knowledge Server 使用说明

本项目提供了两种符合 MCP (Model Context Protocol) 协议的服务器模式：

1. **MCP 标准输入输出服务器** - 使用 stdio 传输，用于与 MCP 客户端直接通信
2. **MCP HTTP 服务器** - 使用 Streamable HTTP 传输，符合 MCP 协议规范

## MCP 协议兼容性

- ✅ 支持 MCP 协议版本：2025-06-18, 2025-03-26, 2024-11-05
- ✅ 支持 stdio 传输（标准输入输出）
- ✅ 支持 Streamable HTTP 传输
- ✅ 支持 JSON-RPC 消息格式
- ✅ 支持会话管理
- ✅ 支持 Server-Sent Events (SSE)
- ✅ 支持工具调用 (tools/list, tools/call)
- ✅ 符合 MCP 安全最佳实践

## 安装依赖

首先安装所需的依赖包：

```bash
pip install -r requirements.txt
```

## 运行方式

### 方式一：使用启动脚本（推荐）

#### 启动 MCP 服务器
```bash
python start_server.py mcp
```

#### 启动 MCP HTTP 服务器
```bash
# 默认配置 (127.0.0.1:8000)
python start_server.py http

# 自定义主机和端口
python start_server.py http --host 0.0.0.0 --port 8080

# 开发模式（自动重载）
python start_server.py http --host 0.0.0.0 --port 8080 --reload
```

### 方式二：直接运行服务器

#### MCP 服务器
```bash
python mcp_server.py --stdio
```

#### MCP HTTP 服务器
```bash
python http_server.py --host 0.0.0.0 --port 8080
```

## 服务器参数说明

### MCP stdio 服务器参数
- `--stdio`: 使用标准输入输出模式（必需）
- `--log-level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)

### MCP HTTP 服务器参数
- `--host`: 服务器主机地址（默认: 127.0.0.1）
- `--port`: 服务器端口（默认: 8000）
- `--log-level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `--reload`: 启用自动重载（开发模式）

## MCP 协议接口

### 1. MCP 主端点
```
POST /mcp
Content-Type: application/json
MCP-Protocol-Version: 2025-06-18
Mcp-Session-Id: <session-id>

{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
        "protocolVersion": "2025-06-18",
        "capabilities": {"tools": {}},
        "clientInfo": {"name": "client", "version": "1.0.0"}
    }
}
```

### 2. SSE 流端点
```
GET /mcp
Accept: text/event-stream
MCP-Protocol-Version: 2025-06-18
Mcp-Session-Id: <session-id>
```

### 3. 会话终止
```
DELETE /mcp
Mcp-Session-Id: <session-id>
```

### 4. 健康检查（非 MCP 协议）
```
GET /health
```

## 支持的 MCP 方法

- `initialize` - 初始化 MCP 连接
- `tools/list` - 列出可用工具
- `tools/call` - 调用指定工具

## 测试服务器

### MCP 协议兼容性测试
```bash
# 完整 MCP 协议测试
python test_mcp_client.py

# 自定义服务器地址和查询文本
python test_mcp_client.py --host 127.0.0.1 --port 8080 --query "人工智能发展"

# 测试特定协议版本
python test_mcp_client.py --protocol-version 2025-06-18
```

### 传统 HTTP API 测试（已弃用）
```bash
# 快速健康检查
python test_server.py --quick

# 完整功能测试
python test_server.py --host 127.0.0.1 --port 8080 --query "人工智能发展"
```

## 日志文件

所有日志文件保存在 `logs/` 目录下：

- `logs/mcp_server.log` - MCP stdio 服务器日志
- `logs/mcp_http_server.log` - MCP HTTP 服务器日志
- `logs/start_server.log` - 启动脚本日志
- `logs/test_mcp_client.log` - MCP 客户端测试日志
- `logs/test_server.log` - 传统 HTTP 测试日志

## 使用示例

### 1. 启动 MCP HTTP 服务器并测试

```bash
# 终端1：启动 MCP HTTP 服务器
python start_server.py http --host 0.0.0.0 --port 8080

# 终端2：测试 MCP 协议兼容性
python test_mcp_client.py --host 127.0.0.1 --port 8080
```

### 2. 使用 MCP 客户端连接

```python
import requests
import json

# MCP 初始化
def mcp_initialize(base_url):
    message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-06-18",
            "capabilities": {"tools": {}},
            "clientInfo": {"name": "my-client", "version": "1.0.0"}
        }
    }

    response = requests.post(
        f"{base_url}/mcp",
        json=message,
        headers={
            "Content-Type": "application/json",
            "Accept": "application/json",
            "MCP-Protocol-Version": "2025-06-18"
        }
    )

    return response.json(), response.headers.get("Mcp-Session-Id")

# 调用工具
def mcp_call_tool(base_url, session_id, tool_name, arguments):
    message = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": arguments
        }
    }

    response = requests.post(
        f"{base_url}/mcp",
        json=message,
        headers={
            "Content-Type": "application/json",
            "Accept": "application/json",
            "MCP-Protocol-Version": "2025-06-18",
            "Mcp-Session-Id": session_id
        }
    )

    return response.json()

# 使用示例
base_url = "http://127.0.0.1:8080"
init_result, session_id = mcp_initialize(base_url)
print("初始化结果:", init_result)

if session_id:
    tool_result = mcp_call_tool(base_url, session_id, "get_knowledge_data", {"query": "人工智能"})
    print("工具调用结果:", tool_result)
```

## 故障排除

### 1. 端口被占用
如果遇到端口被占用的错误，请更换端口：
```bash
python start_server.py http --port 8081
```

### 2. 依赖包缺失
确保安装了所有依赖：
```bash
pip install -r requirements.txt
```

### 3. 知识库连接失败
检查 `config.json` 中的数据库配置是否正确。

### 4. 查看详细日志
设置日志级别为 DEBUG：
```bash
python start_server.py http --log-level DEBUG
```

## 开发模式

在开发过程中，建议使用自动重载模式：

```bash
python start_server.py http --reload --log-level DEBUG
```

这样当代码发生变化时，服务器会自动重启。
