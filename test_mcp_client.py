#!/usr/bin/env python3
"""
MCP 客户端测试脚本
用于测试 MCP HTTP 服务器的协议兼容性
"""

import requests
import json
import argparse
import time
from typing import Optional, Dict, Any
from logger import Logger

# 设置日志
logger = Logger(log_file="logs/test_mcp_client.log", name="MCP_Client_Tester")

class MCPClient:
    """MCP HTTP 客户端"""
    
    def __init__(self, base_url: str, protocol_version: str = "2025-06-18"):
        self.base_url = base_url
        self.mcp_endpoint = f"{base_url}/mcp"
        self.protocol_version = protocol_version
        self.session_id: Optional[str] = None
        self.request_id = 0
    
    def _get_next_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
            "MCP-Protocol-Version": self.protocol_version
        }
        if self.session_id:
            headers["Mcp-Session-Id"] = self.session_id
        return headers
    
    def send_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送 JSON-RPC 请求"""
        message = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": method
        }
        if params:
            message["params"] = params
        
        logger.info(f"发送请求: {method}")
        
        try:
            response = requests.post(
                self.mcp_endpoint,
                json=message,
                headers=self._get_headers(),
                timeout=30
            )
            
            # 检查会话ID
            if "Mcp-Session-Id" in response.headers:
                self.session_id = response.headers["Mcp-Session-Id"]
                logger.info(f"收到会话ID: {self.session_id}")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"请求成功: {method}")
                return result
            else:
                logger.error(f"请求失败: {response.status_code} - {response.text}")
                return {"error": {"code": response.status_code, "message": response.text}}
                
        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            return {"error": {"code": -1, "message": str(e)}}
    
    def initialize(self) -> bool:
        """初始化 MCP 连接"""
        logger.info("初始化 MCP 连接...")
        
        params = {
            "protocolVersion": self.protocol_version,
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "test-mcp-client",
                "version": "1.0.0"
            }
        }
        
        result = self.send_request("initialize", params)
        
        if "error" in result:
            logger.error(f"初始化失败: {result['error']}")
            return False
        
        if "result" in result:
            server_info = result["result"]
            logger.info(f"初始化成功:")
            logger.info(f"  服务器协议版本: {server_info.get('protocolVersion', 'unknown')}")
            logger.info(f"  服务器名称: {server_info.get('serverInfo', {}).get('name', 'unknown')}")
            logger.info(f"  服务器版本: {server_info.get('serverInfo', {}).get('version', 'unknown')}")
            return True
        
        return False
    
    def list_tools(self) -> bool:
        """列出可用工具"""
        logger.info("获取工具列表...")
        
        result = self.send_request("tools/list")
        
        if "error" in result:
            logger.error(f"获取工具列表失败: {result['error']}")
            return False
        
        if "result" in result:
            tools = result["result"].get("tools", [])
            logger.info(f"可用工具数量: {len(tools)}")
            for tool in tools:
                logger.info(f"  - {tool.get('name', 'unknown')}: {tool.get('description', 'no description')}")
            return True
        
        return False
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> bool:
        """调用工具"""
        logger.info(f"调用工具: {tool_name}")
        
        params = {
            "name": tool_name,
            "arguments": arguments
        }
        
        result = self.send_request("tools/call", params)
        
        if "error" in result:
            logger.error(f"工具调用失败: {result['error']}")
            return False
        
        if "result" in result:
            content = result["result"].get("content", [])
            logger.info(f"工具调用成功，返回内容数量: {len(content)}")
            for item in content:
                if item.get("type") == "text":
                    text = item.get("text", "")
                    preview = text[:200] + "..." if len(text) > 200 else text
                    logger.info(f"  文本内容预览: {preview}")
            return True
        
        return False
    
    def terminate_session(self) -> bool:
        """终止会话"""
        if not self.session_id:
            logger.info("没有活跃会话需要终止")
            return True
        
        logger.info(f"终止会话: {self.session_id}")
        
        try:
            response = requests.delete(
                self.mcp_endpoint,
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("会话终止成功")
                self.session_id = None
                return True
            else:
                logger.error(f"会话终止失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"会话终止异常: {str(e)}")
            return False

def test_mcp_protocol(base_url: str, query_text: str) -> bool:
    """测试 MCP 协议兼容性"""
    logger.info("=" * 60)
    logger.info("开始 MCP 协议兼容性测试")
    logger.info(f"目标服务器: {base_url}")
    logger.info("=" * 60)
    
    client = MCPClient(base_url)
    
    tests = [
        ("初始化连接", lambda: client.initialize()),
        ("获取工具列表", lambda: client.list_tools()),
        ("调用知识查询工具", lambda: client.call_tool("get_knowledge_data", {"query": query_text})),
        ("终止会话", lambda: client.terminate_session()),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n正在测试: {test_name}")
        if test_func():
            passed += 1
            logger.info(f"✓ {test_name} - 通过")
        else:
            logger.error(f"✗ {test_name} - 失败")
        time.sleep(1)  # 间隔1秒
    
    logger.info("\n" + "=" * 60)
    logger.info(f"MCP 协议测试完成: {passed}/{total} 项测试通过")
    if passed == total:
        logger.info("✓ 所有测试通过，MCP 协议兼容性良好")
    else:
        logger.warning(f"✗ {total - passed} 项测试失败，请检查 MCP 协议实现")
    logger.info("=" * 60)
    
    return passed == total

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MCP 协议兼容性测试工具")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址 (默认: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--query", default="人工智能", help="测试查询文本 (默认: 人工智能)")
    parser.add_argument("--protocol-version", default="2025-06-18", 
                       choices=["2025-06-18", "2025-03-26", "2024-11-05"],
                       help="MCP 协议版本 (默认: 2025-06-18)")
    
    args = parser.parse_args()
    
    base_url = f"http://{args.host}:{args.port}"
    
    # 首先检查服务器是否可达
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✓ 服务器健康检查通过")
        else:
            logger.error("✗ 服务器健康检查失败")
            return
    except Exception as e:
        logger.error(f"✗ 无法连接到服务器: {str(e)}")
        return
    
    # 运行 MCP 协议测试
    test_mcp_protocol(base_url, args.query)

if __name__ == "__main__":
    main()
