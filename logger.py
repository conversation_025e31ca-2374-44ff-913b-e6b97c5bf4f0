import logging
class Logger:
    def __init__(self, log_file: str, name: str):
        self.log_file = log_file
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        self.formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        self.file_handler = logging.FileHandler(self.log_file, mode='w', encoding='utf-8')
        self.file_handler.setFormatter(self.formatter)
        self.logger.addHandler(self.file_handler)
        self.console_handler = logging.StreamHandler()
        self.console_handler.setFormatter(self.formatter)
        self.logger.addHandler(self.console_handler)
        
    def info(self, message: str, *args, **kwargs):
        self.logger.info(message, *args, **kwargs)
        
    def error(self, message: str, *args, **kwargs):
        self.logger.error(message, *args, **kwargs)
        
    def warning(self, message: str, *args, **kwargs):
        self.logger.warning(message, *args, **kwargs)
        
    def debug(self, message: str, *args, **kwargs):
        self.logger.debug(message, *args, **kwargs)